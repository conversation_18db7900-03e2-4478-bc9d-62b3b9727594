<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ProductController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/about', function () {
    return view('about', ['title' => 'About Us']);
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/analytics', function () {
        return view('analytics');
    })->name('analytics');

    Route::get('/projects', function () {
        return view('projects.index');
    })->name('projects.index');

    Route::get('/projects/create', function () {
        return view('projects.create');
    })->name('projects.create');

    Route::get('/tasks', function () {
        return view('tasks.index');
    })->name('tasks.index');

    Route::get('/tasks/create', function () {
        return view('tasks.create');
    })->name('tasks.create');

    Route::get('/team', function () {
        return view('team.index');
    })->name('team.index');

    Route::get('/team/invite', function () {
        return view('team.invite');
    })->name('team.invite');

    Route::get('/reports', function () {
        return view('reports.index');
    })->name('reports.index');

    Route::get('/reports/generate', function () {
        return view('reports.generate');
    })->name('reports.generate');

    Route::get('/settings', function () {
        return view('settings.index');
    })->name('settings.index');
});

Route::get('/user/{id}', function ($id) {
    return view('user.show', ['userId' => $id]);
})->where('id', '[0-9]+')->name('user.show')->middleware('auth');

Route::get('/user/{id}/posts/{slug}', function ($id, $slug) {
    return view('user.posts', ['userId' => $id, 'slug' => $slug]);
})->where(['id' => '[0-9]+', 'slug' => '[a-z-]+'])->name('user.posts')->middleware('auth');

Route::get('/posts/{category?}', function ($category = 'general') {
    return "Posts in category: " . $category;
})->name('posts.category');

Route::get('/products/{id}', function ($id) {
    return "Product ID: " . $id;
})->name('products.show')->where('id', '[0-9]+');

Route::get('/search/{term}', function ($term) {
    return "Searching for: " . $term;
})->name('search.results');

Route::prefix('admin')->middleware(['auth', 'verified'])->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return "Admin Dashboard";
    })->name('dashboard');

    Route::get('/users', function () {
        return "Admin Users List";
    })->name('users');

    Route::get('/settings', function () {
        return "Admin Settings";
    })->name('settings');
});

Route::prefix('api/v1')->middleware('auth:sanctum')->name('api.')->group(function () {
    Route::get('/users', function () {
        return response()->json(['users' => 'API Users List']);
    })->name('users');

    Route::get('/posts', function () {
        return response()->json(['posts' => 'API Posts List']);
    })->name('posts');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::get('/premium', function () {
    return "Premium Content - Only for premium users";
})->middleware(['auth', 'premium'])->name('premium.content');

Route::get('/admin-only', function () {
    return "Admin Only Content";
})->middleware(['auth', 'admin'])->name('admin.only');

Route::get('/public', function () {
    return "This is a public page accessible to everyone";
})->name('public.page');

Route::get('/members', function () {
    return "Members only content";
})->middleware('auth')->name('members.only');

Route::get('/verified-users', function () {
    return "Verified users only content";
})->middleware(['auth', 'verified'])->name('verified.only');

Route::get('/redirect-test', function () {
    return redirect()->route('dashboard');
})->name('redirect.test');

Route::get('/login-redirect', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('login');
})->name('login.redirect');

Route::resource('blogs', BlogController::class)->middleware('auth');
Route::resource('posts', BlogController::class)->only(['index', 'show']);
Route::resource('comments', BlogController::class)->except(methods: ['create', 'edit']);

Route::resource('products', controller: ProductController::class)->except(methods: ['index', 'create']);

require __DIR__.'/auth.php';
