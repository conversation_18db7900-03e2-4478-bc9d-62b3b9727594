<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// ============================================================================
// SESSION 3: ADVANCED ROUTING DEMONSTRATION
// 6 Different Ways to Define Routes in Laravel
// ============================================================================

// 1. BASIC CLOSURE ROUTES
// Simple routes with anonymous functions
Route::get('/', function () {
    return view('welcome');
});

Route::get('/about', function () {
    return view('about', ['title' => 'About Us']);
});

// 2. CONTROLLER-BASED ROUTES
// Routes pointing to controller methods
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// 3. ROUTE PARAMETERS AND CONSTRAINTS
// Routes with dynamic parameters and validation
Route::get('/user/{id}', function ($id) {
    return "User ID: " . $id;
})->where('id', '[0-9]+')->name('user.show');

Route::get('/user/{id}/posts/{slug}', function ($id, $slug) {
    return "User {$id}, Post: {$slug}";
})->where(['id' => '[0-9]+', 'slug' => '[a-z-]+']);

// Optional parameters
Route::get('/posts/{category?}', function ($category = 'general') {
    return "Posts in category: " . $category;
})->name('posts.category');

// 4. NAMED ROUTES WITH ROUTE MODEL BINDING
// Named routes for easy URL generation
Route::get('/products/{id}', function ($id) {
    return "Product ID: " . $id;
})->name('products.show')->where('id', '[0-9]+');

Route::get('/search/{term}', function ($term) {
    return "Searching for: " . $term;
})->name('search.results');

// 5. ROUTE GROUPS WITH MIDDLEWARE AND PREFIXES
// Grouping routes with common attributes

// Admin routes group
Route::prefix('admin')->middleware(['auth', 'verified'])->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return "Admin Dashboard";
    })->name('dashboard');

    Route::get('/users', function () {
        return "Admin Users List";
    })->name('users');

    Route::get('/settings', function () {
        return "Admin Settings";
    })->name('settings');
});

// API routes group
Route::prefix('api/v1')->middleware('auth:sanctum')->name('api.')->group(function () {
    Route::get('/users', function () {
        return response()->json(['users' => 'API Users List']);
    })->name('users');

    Route::get('/posts', function () {
        return response()->json(['posts' => 'API Posts List']);
    })->name('posts');
});

// 6. RESOURCE ROUTES AND ROUTE CACHING
// RESTful resource routes (will be demonstrated with controllers)

// Profile routes (existing Breeze routes)
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// ============================================================================
// MIDDLEWARE DEMONSTRATION ROUTES
// ============================================================================

// Routes with custom middleware (will be created)
Route::get('/premium', function () {
    return "Premium Content - Only for premium users";
})->middleware(['auth', 'premium'])->name('premium.content');

Route::get('/admin-only', function () {
    return "Admin Only Content";
})->middleware(['auth', 'admin'])->name('admin.only');

// ============================================================================
// AUTHENTICATION FLOW DEMONSTRATION
// ============================================================================

// Public routes
Route::get('/public', function () {
    return "This is a public page accessible to everyone";
})->name('public.page');

// Protected routes with different access levels
Route::get('/members', function () {
    return "Members only content";
})->middleware('auth')->name('members.only');

Route::get('/verified-users', function () {
    return "Verified users only content";
})->middleware(['auth', 'verified'])->name('verified.only');

// Redirect routes for demonstration
Route::get('/redirect-test', function () {
    return redirect()->route('dashboard');
})->name('redirect.test');

Route::get('/login-redirect', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('login');
})->name('login.redirect');

// ============================================================================
// RESOURCE ROUTES DEMONSTRATION
// ============================================================================

// Full resource routes (7 routes: index, create, store, show, edit, update, destroy)
use App\Http\Controllers\BlogController;

Route::resource('blogs', BlogController::class)->middleware('auth');

// Partial resource routes
Route::resource('posts', BlogController::class)->only(['index', 'show']);
Route::resource('comments', BlogController::class)->except(['create', 'edit']);

// ============================================================================
// ROUTE CACHING AND OPTIMIZATION
// ============================================================================

// These routes are optimized for caching
// Use: php artisan route:cache (in production)
// Clear: php artisan route:clear

// Include authentication routes
require __DIR__.'/auth.php';
