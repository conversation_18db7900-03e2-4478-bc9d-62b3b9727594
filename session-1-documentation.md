# Session 1: Laravel Framework & Installation Setup

## Hour 1: Theory and Training

### Introduction to Laravel Framework

Laravel is a powerful, elegant PHP web application framework that follows the Model-View-Controller (MVC) architectural pattern. Created by <PERSON> in 2011, Laravel has become one of the most popular PHP frameworks.

#### Key Features of Laravel:
- **Elegant Syntax**: Clean, expressive syntax that makes development enjoyable
- **MVC Architecture**: Separates application logic, presentation, and data
- **Artisan CLI**: Powerful command-line interface for development tasks
- **Eloquent ORM**: Beautiful, simple ActiveRecord implementation for database operations
- **Blade Templating**: Powerful templating engine with inheritance and sections
- **Built-in Authentication**: Ready-to-use authentication system
- **Routing**: Simple and flexible routing system
- **Middleware**: HTTP middleware for filtering requests
- **Database Migrations**: Version control for your database schema
- **Testing**: Built-in testing support with PHPUnit

#### Laravel Philosophy:
- Convention over Configuration
- Developer Happiness
- Rapid Application Development
- Clean, readable code

### Installation and Environment Setup

#### System Requirements:
- PHP >= 8.2
- Composer (PHP dependency manager)
- Node.js & NPM (for frontend assets)
- Database (MySQL, PostgreSQL, SQLite, SQL Server)

#### Installation Methods:
1. **Via Composer Create-Project**: `composer create-project laravel/laravel project-name`
2. **Via Laravel Installer**: `laravel new project-name`
3. **Via Git Clone**: Clone from repository and run `composer install`

#### Environment Configuration:
- `.env` file contains environment-specific settings
- Never commit `.env` to version control
- Use `.env.example` as a template
- Generate application key: `php artisan key:generate`

### Project Structure Overview

Laravel follows a well-organized directory structure that promotes clean code organization:

```
laravel-project/
├── app/                    # Application core files
│   ├── Http/              # Controllers, Middleware, Requests
│   ├── Models/            # Eloquent models
│   ├── Providers/         # Service providers
│   └── ...
├── bootstrap/             # Framework bootstrap files
├── config/                # Configuration files
├── database/              # Migrations, factories, seeders
├── public/                # Web server document root
├── resources/             # Views, raw assets, language files
├── routes/                # Route definitions
├── storage/               # Logs, cache, sessions, uploads
├── tests/                 # Automated tests
├── vendor/                # Composer dependencies
├── .env                   # Environment configuration
├── artisan                # Artisan command-line tool
├── composer.json          # PHP dependencies
└── package.json           # Node.js dependencies
```

## Hour 2: Hands-on Practice

### Current Installation Status:
- ✅ Laravel Framework: 12.19.3
- ✅ PHP Version: 8.2.4
- ✅ Environment: Configured
- ✅ Database: MySQL configured (session_two)

### Directory Structure Exploration

#### Key Directories Explained:

1. **app/** - Contains the core application code
2. **config/** - All configuration files
3. **database/** - Database-related files
4. **public/** - Web-accessible files
5. **resources/** - Views and raw assets
6. **routes/** - Application routes
7. **storage/** - Generated files and logs
8. **vendor/** - Third-party packages

### Configuration Files (.env and config/)

The `.env` file contains environment-specific configuration:
- Database connection settings
- Application settings (name, environment, debug mode)
- Cache and session configuration
- Mail configuration
- Third-party service keys

The `config/` directory contains organized configuration files:
- `app.php` - Application settings
- `database.php` - Database connections
- `auth.php` - Authentication settings
- `cache.php` - Cache configuration
- And many more...

### Basic Artisan Commands

Artisan is Laravel's command-line interface that provides helpful commands for development.

#### Essential Commands:
- `php artisan list` - Show all available commands
- `php artisan serve` - Start development server
- `php artisan migrate` - Run database migrations
- `php artisan make:controller` - Create a controller
- `php artisan make:model` - Create a model
- `php artisan make:migration` - Create a migration
- `php artisan route:list` - Show all routes
- `php artisan config:cache` - Cache configuration
- `php artisan config:clear` - Clear configuration cache

### Next Steps:
Session 1 provides the foundation for Laravel development. In Session 2, we'll install Laravel Breeze for authentication and explore frontend integration.
