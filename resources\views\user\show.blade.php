<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('User Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h1 class="text-3xl font-bold text-gray-900">User Profile #{{ $userId }}</h1>
                        <p class="text-gray-600 mt-2">Viewing user profile and information</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- User Information Card -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold mb-4">User Information</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="font-medium text-gray-700">User ID:</span>
                                    <span class="text-gray-900">{{ $userId }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Profile Type:</span>
                                    <span class="text-gray-900">{{ $userId % 2 == 0 ? 'Premium User' : 'Standard User' }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Status:</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $userId % 3 == 0 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                        {{ $userId % 3 == 0 ? 'Active' : 'Online' }}
                                    </span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Member Since:</span>
                                    <span class="text-gray-900">{{ now()->subDays($userId * 10)->format('M d, Y') }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- User Stats Card -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold mb-4">User Statistics</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-700">Total Posts:</span>
                                    <span class="font-semibold">{{ $userId * 3 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-700">Comments:</span>
                                    <span class="font-semibold">{{ $userId * 7 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-700">Likes Received:</span>
                                    <span class="font-semibold">{{ $userId * 15 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-700">Reputation:</span>
                                    <span class="font-semibold text-green-600">{{ $userId * 25 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Posts Section -->
                    <div class="mt-8">
                        <h3 class="text-xl font-semibold mb-4">Recent Posts</h3>
                        <div class="space-y-4">
                            @for($i = 1; $i <= 3; $i++)
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="font-medium text-gray-900">
                                                <a href="{{ route('user.posts', ['id' => $userId, 'slug' => 'post-' . $i]) }}" 
                                                   class="hover:text-blue-600 transition-colors">
                                                    Sample Post {{ $i }} by User {{ $userId }}
                                                </a>
                                            </h4>
                                            <p class="text-gray-600 mt-1">This is a sample post content preview for demonstration purposes...</p>
                                            <div class="flex items-center mt-2 text-sm text-gray-500">
                                                <span>{{ now()->subDays($i)->format('M d, Y') }}</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ rand(5, 50) }} comments</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ rand(10, 100) }} likes</span>
                                            </div>
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            Published
                                        </span>
                                    </div>
                                </div>
                            @endfor
                        </div>
                    </div>

                    <!-- Navigation Links -->
                    <div class="mt-8 flex space-x-4">
                        <a href="{{ route('dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 transition-colors">
                            ← Back to Dashboard
                        </a>
                        <a href="{{ route('user.posts', ['id' => $userId, 'slug' => 'all-posts']) }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 transition-colors">
                            View All Posts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
