<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Products</h1>
            <a href="<?php echo e(route('products.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add New Product
            </a>
        </div>

        <?php if($products->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <?php if($product->image): ?>
                            <img src="<?php echo e(asset('storage/' . $product->image)); ?>" alt="<?php echo e($product->title); ?>" class="w-full h-48 object-cover">
                        <?php endif; ?>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold mb-2"><?php echo e($product->title); ?></h3>
                            <p class="text-gray-600 text-sm"><?php echo e(Str::limit($product->description, 100)); ?></p>
                            <p class="text-gray-500 text-xs mt-2">Created: <?php echo e($product->created_at->format('M d, Y')); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first product.</p>
                <a href="<?php echo e(route('products.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Create First Product
                </a>
            </div>
        <?php endif; ?>

        <div class="mt-8">
            <a href="<?php echo e(route('dashboard')); ?>" class="text-blue-500 hover:text-blue-700">← Back to Dashboard</a>
        </div>
    </div>
</body>
</html>tion('content')

<?php $__env->stopSection(); ?><?php /**PATH D:\laravel-sessions\session-two\resources\views/products/index.blade.php ENDPATH**/ ?>