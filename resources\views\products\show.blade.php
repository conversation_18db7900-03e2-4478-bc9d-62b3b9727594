@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Product Details</h4>
                    <div class="btn-group" role="group">
                        <a href="{{ route('products.edit', $product) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('products.destroy', $product) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to delete this product?')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            @if($product->image)
                                <img src="{{ asset('storage/' . $product->image) }}" 
                                     class="img-fluid rounded shadow" 
                                     alt="{{ $product->title }}"
                                     style="width: 100%; max-height: 400px; object-fit: cover;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                     style="height: 400px;">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-5x text-muted mb-3"></i>
                                        <p class="text-muted">No image available</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                        
                        <div class="col-md-6">
                            <h1 class="h2 mb-3">{{ $product->title }}</h1>
                            
                            <div class="mb-4">
                                <h5 class="text-muted">Description</h5>
                                <p class="lead">{{ $product->description }}</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="mb-3">
                                        <h6 class="text-muted">Created Date</h6>
                                        <p class="mb-0">
                                            <i class="fas fa-calendar-alt text-primary"></i>
                                            {{ $product->created_at->format('F d, Y') }}
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="col-sm-6">
                                    <div class="mb-3">
                                        <h6 class="text-muted">Last Updated</h6>
                                        <p class="mb-0">
                                            <i class="fas fa-clock text-success"></i>
                                            {{ $product->updated_at->format('F d, Y') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-muted">Product ID</h6>
                                <p class="mb-0">
                                    <code>#{{ $product->id }}</code>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ route('products.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Products
                        </a>
                        
                        <div class="btn-group" role="group">
                            <a href="{{ route('products.edit', $product) }}" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Product
                            </a>
                            <a href="{{ route('products.create') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add New Product
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Add any JavaScript for the product show page here
    document.addEventListener('DOMContentLoaded', function() {
        // Image zoom functionality (optional)
        const productImage = document.querySelector('.img-fluid');
        if (productImage) {
            productImage.addEventListener('click', function() {
                // You can add image modal or zoom functionality here
                console.log('Product image clicked');
            });
        }
    });
</script>
@endsection
