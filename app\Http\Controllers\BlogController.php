<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return "Blog Index - List of all blog posts";
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return "Blog Create - Form to create new blog post";
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return "Blog Store - Save new blog post";
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return "Blog Show - Display blog post ID: " . $id;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return "Blog Edit - Form to edit blog post ID: " . $id;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return "Blog Update - Update blog post ID: " . $id;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return "Blog Destroy - Delete blog post ID: " . $id;
    }
}
