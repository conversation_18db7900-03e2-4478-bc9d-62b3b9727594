<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function index()
    {
        return "Blog Index - List of all blog posts";
    }

    public function create()
    {
        return "Blog Create - Form to create new blog post";
    }

    public function store(Request $request)
    {
        return "Blog Store - Save new blog post";
    }

    public function show(string $id)
    {
        return "Blog Show - Display blog post ID: " . $id;
    }

    public function edit(string $id)
    {
        return "Blog Edit - Form to edit blog post ID: " . $id;
    }

    public function update(Request $request, string $id)
    {
        return "Blog Update - Update blog post ID: " . $id;
    }

    public function destroy(string $id)
    {
        return "Blog Destroy - Delete blog post ID: " . $id;
    }
}
