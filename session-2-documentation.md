# Session 2: <PERSON><PERSON> Breeze Installation & Authentication Setup

## Hour 1: Theory and Training

### Lara<PERSON> Breeze Overview

Laravel Breeze is a minimal, simple implementation of <PERSON><PERSON>'s authentication features including:
- User registration
- Login/logout
- Password reset
- Email verification
- Profile management

#### Why <PERSON><PERSON> Breeze?
- **Lightweight**: Minimal dependencies and clean code
- **Customizable**: Easy to modify and extend
- **Modern**: Uses Tailwind CSS and Alpine.js
- **Complete**: Full authentication flow out of the box
- **Educational**: Great for learning Laravel authentication

#### Breeze vs Other Options:
- **<PERSON><PERSON> UI**: Older, uses Bootstrap
- **Jetstream**: More features (teams, API tokens) but more complex
- **Breeze**: Perfect balance of features and simplicity

### Authentication Scaffolding Components

#### 1. Controllers (`app/Http/Controllers/Auth/`):
- `AuthenticatedSessionController` - Login/logout
- `RegisteredUserController` - User registration
- `PasswordResetLinkController` - Password reset emails
- `NewPasswordController` - Password reset form
- `EmailVerificationController` - Email verification
- `ProfileController` - User profile management

#### 2. Routes (`routes/auth.php`):
- Guest routes (login, register, password reset)
- Authenticated routes (logout, email verification, profile)
- Middleware protection for appropriate routes

#### 3. Views (`resources/views/`):
- `auth/` - Authentication forms
- `layouts/` - Layout templates
- `components/` - Reusable UI components
- `profile/` - Profile management

#### 4. Middleware:
- `auth` - Requires authentication
- `guest` - Only for non-authenticated users
- `verified` - Requires email verification

## Hour 2: Hands-on Practice

### Installation Process Completed

#### 1. Laravel Breeze Installation:
```bash
composer require laravel/breeze --dev
php artisan breeze:install blade
```

#### 2. Frontend Dependencies:
- Node.js packages automatically installed
- Tailwind CSS configured
- Vite build tool set up
- Assets compiled successfully

### Authentication System Analysis

#### Current Routes (22 total):
```
Authentication Routes:
├── GET  /login              # Login form
├── POST /login              # Process login
├── GET  /register           # Registration form
├── POST /register           # Process registration
├── POST /logout             # Logout user
├── GET  /forgot-password    # Password reset request
├── POST /forgot-password    # Send reset email
├── GET  /reset-password     # Password reset form
├── POST /reset-password     # Process password reset
├── GET  /verify-email       # Email verification notice
├── GET  /verify-email/{id}  # Verify email link
└── POST /email/verification # Resend verification

Protected Routes:
├── GET  /dashboard          # User dashboard
├── GET  /profile            # Profile edit form
├── PATCH /profile           # Update profile
└── DELETE /profile          # Delete account
```

#### Middleware Protection:
- **Guest middleware**: Login, register, password reset (only for non-authenticated)
- **Auth middleware**: Dashboard, profile, logout (requires authentication)
- **Verified middleware**: Dashboard (requires email verification)

### Blade Templates Structure

#### 1. Layout Templates:
- `layouts/app.blade.php` - Main application layout
- `layouts/guest.blade.php` - Guest pages layout
- `layouts/navigation.blade.php` - Navigation component

#### 2. Authentication Views:
- `auth/login.blade.php` - Login form
- `auth/register.blade.php` - Registration form
- `auth/forgot-password.blade.php` - Password reset request
- `auth/reset-password.blade.php` - Password reset form
- `auth/verify-email.blade.php` - Email verification notice

#### 3. Reusable Components:
- `components/text-input.blade.php` - Form inputs
- `components/primary-button.blade.php` - Buttons
- `components/input-error.blade.php` - Error messages
- `components/input-label.blade.php` - Form labels

### Frontend Dependencies

#### Installed Packages:
- **Tailwind CSS**: Utility-first CSS framework
- **Alpine.js**: Lightweight JavaScript framework
- **Vite**: Fast build tool
- **Axios**: HTTP client for API requests

#### Build Process:
```bash
npm run dev     # Development build with hot reload
npm run build   # Production build
npm run watch   # Watch for changes
```

### Database Schema

#### Users Table (already migrated):
```sql
- id (primary key)
- name (string)
- email (unique string)
- email_verified_at (timestamp, nullable)
- password (hashed string)
- remember_token (string, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

## Session 2 Completion Status

✅ Laravel Breeze installed successfully (v2.3.7)
✅ Authentication scaffolding generated
✅ Frontend dependencies installed and built
✅ 22 authentication routes created
✅ Complete authentication views generated
✅ Middleware protection configured
✅ Database migrations ready
✅ Tailwind CSS and Alpine.js integrated

## Next Session Preview

In Session 3, we will:
- Explore 6 different routing methods in Laravel
- Implement custom middleware
- Create route groups and named routes
- Set up advanced authentication flows
- Test route protection and redirection
- Build practical routing examples

The authentication system is now ready for advanced routing and middleware implementation!
