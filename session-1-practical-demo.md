# Session 1: Practical Demonstration

## Current Laravel Installation Status

### Environment Details:
- **Laravel Version**: 12.19.3
- **PHP Version**: 8.2.4
- **Database**: MySQL (session_two)
- **Environment**: Local Development

## Project Structure Exploration

### 1. Application Core (`app/` directory)

The `app/` directory contains your application's core code:

```
app/
├── Http/
│   └── Controllers/     # Request handlers
├── Models/
│   └── User.php        # Default User model
└── Providers/
    └── AppServiceProvider.php
```

#### Key Model Example - User.php:
```php
<?php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;
    
    protected $fillable = ['name', 'email', 'password'];
    protected $hidden = ['password', 'remember_token'];
    
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
```

### 2. Configuration Files

#### Environment Configuration (.env):
```env
APP_NAME=Laravel
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=session_two
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
CACHE_STORE=database
QUEUE_CONNECTION=database
```

#### Key Configuration Directories:
- `config/app.php` - Application settings
- `config/database.php` - Database connections
- `config/auth.php` - Authentication configuration

### 3. Routes and Current Application State

#### Current Routes (from `php artisan route:list`):
```
GET|HEAD  /                    # Welcome page
GET|HEAD  storage/{path}       # File storage access
GET|HEAD  up                   # Health check
```

#### Default Route (`routes/web.php`):
```php
<?php
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});
```

## Essential Artisan Commands Demonstrated

### 1. Information Commands:
```bash
php artisan --version          # Show Laravel version
php artisan list              # List all available commands
php artisan route:list        # Show all registered routes
php artisan config:show       # Show configuration values
```

### 2. Development Commands:
```bash
php artisan serve             # Start development server
php artisan migrate           # Run database migrations
php artisan migrate:status    # Check migration status
php artisan config:cache      # Cache configuration
php artisan config:clear      # Clear configuration cache
```

### 3. Code Generation Commands:
```bash
php artisan make:controller UserController
php artisan make:model Post
php artisan make:migration create_posts_table
php artisan make:middleware CheckAge
php artisan make:request StoreUserRequest
```

## Directory Structure Summary

### Core Directories:
1. **`app/`** - Application logic (Models, Controllers, Middleware)
2. **`config/`** - Configuration files
3. **`database/`** - Migrations, seeders, factories
4. **`public/`** - Web-accessible files (index.php, assets)
5. **`resources/`** - Views, CSS, JS, language files
6. **`routes/`** - Route definitions (web.php, api.php)
7. **`storage/`** - Generated files, logs, cache
8. **`vendor/`** - Composer dependencies

### Important Files:
- **`.env`** - Environment configuration
- **`composer.json`** - PHP dependencies
- **`package.json`** - Node.js dependencies
- **`artisan`** - Command-line interface
- **`vite.config.js`** - Frontend build configuration

## Session 1 Completion Checklist

✅ Laravel installation verified (v12.19.3)
✅ PHP environment confirmed (v8.2.4)
✅ Project structure explored
✅ Configuration files reviewed
✅ Basic Artisan commands demonstrated
✅ Default routes examined
✅ User model structure analyzed

## Next Session Preview

In Session 2, we will:
- Install Laravel Breeze for authentication
- Set up frontend dependencies (Node.js/NPM)
- Configure user registration and login
- Customize authentication views
- Test the complete authentication flow

The foundation is now set for building a complete Laravel application with authentication!
