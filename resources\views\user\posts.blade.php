<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('User Posts') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h1 class="text-3xl font-bold text-gray-900">Posts by User #{{ $userId }}</h1>
                        <p class="text-gray-600 mt-2">
                            @if($slug === 'all-posts')
                                Viewing all posts by this user
                            @else
                                Viewing post: <span class="font-medium">{{ str_replace('-', ' ', $slug) }}</span>
                            @endif
                        </p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium text-blue-900">User #{{ $userId }}</h3>
                                <p class="text-blue-700 text-sm">{{ $userId * 3 }} total posts • Member since {{ now()->subDays($userId * 10)->format('M Y') }}</p>
                            </div>
                            <a href="{{ route('user.show', $userId) }}" 
                               class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                View Profile →
                            </a>
                        </div>
                    </div>

                    @if($slug === 'all-posts')
                        <div class="space-y-6">
                            @for($i = 1; $i <= 6; $i++)
                                <article class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start mb-4">
                                        <div class="flex-1">
                                            <h2 class="text-xl font-semibold text-gray-900 mb-2">
                                                <a href="{{ route('user.posts', ['id' => $userId, 'slug' => 'post-' . $i]) }}" 
                                                   class="hover:text-blue-600 transition-colors">
                                                    Advanced Laravel Tutorial {{ $i }} - User {{ $userId }}
                                                </a>
                                            </h2>
                                            <p class="text-gray-600 mb-3">
                                                This is a comprehensive tutorial covering advanced Laravel concepts including routing, middleware, authentication, and more. Perfect for developers looking to enhance their Laravel skills...
                                            </p>
                                            <div class="flex items-center text-sm text-gray-500">
                                                <span>{{ now()->subDays($i * 2)->format('M d, Y') }}</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ rand(5, 50) }} comments</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ rand(10, 200) }} views</span>
                                                <span class="mx-2">•</span>
                                                <span class="text-green-600">{{ rand(5, 25) }} likes</span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full {{ $i % 3 == 0 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                                {{ $i % 3 == 0 ? 'Featured' : 'Published' }}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        @foreach(['Laravel', 'PHP', 'Web Development', 'Tutorial'] as $tag)
                                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-700">
                                                {{ $tag }}
                                            </span>
                                        @endforeach
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <a href="{{ route('user.posts', ['id' => $userId, 'slug' => 'post-' . $i]) }}" 
                                           class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                            Read More →
                                        </a>
                                        <div class="flex space-x-3 text-sm">
                                            <button class="text-gray-500 hover:text-red-600 transition-colors">
                                                ♥ Like
                                            </button>
                                            <button class="text-gray-500 hover:text-blue-600 transition-colors">
                                                💬 Comment
                                            </button>
                                            <button class="text-gray-500 hover:text-green-600 transition-colors">
                                                📤 Share
                                            </button>
                                        </div>
                                    </div>
                                </article>
                            @endfor
                        </div>
                    @else
                        <article class="prose max-w-none">
                            <div class="mb-6">
                                <h1 class="text-3xl font-bold text-gray-900 mb-4">
                                    {{ ucwords(str_replace('-', ' ', $slug)) }} - Advanced Laravel Guide
                                </h1>
                                <div class="flex items-center text-sm text-gray-500 mb-6">
                                    <span>Published {{ now()->subDays(rand(1, 30))->format('M d, Y') }}</span>
                                    <span class="mx-2">•</span>
                                    <span>{{ rand(5, 15) }} min read</span>
                                    <span class="mx-2">•</span>
                                    <span>{{ rand(100, 1000) }} views</span>
                                </div>
                            </div>

                            <div class="bg-gray-50 border-l-4 border-blue-500 p-4 mb-6">
                                <p class="text-gray-700">
                                    <strong>Post Slug:</strong> {{ $slug }}<br>
                                    <strong>Author:</strong> User #{{ $userId }}<br>
                                    <strong>Category:</strong> Laravel Development
                                </p>
                            </div>

                            <div class="space-y-4 text-gray-700">
                                <p>
                                    Welcome to this comprehensive Laravel tutorial! In this post, we'll explore advanced concepts 
                                    that will help you become a more proficient Laravel developer.
                                </p>
                                
                                <h2 class="text-2xl font-semibold text-gray-900 mt-8 mb-4">What You'll Learn</h2>
                                <ul class="list-disc list-inside space-y-2">
                                    <li>Advanced routing techniques and patterns</li>
                                    <li>Custom middleware implementation</li>
                                    <li>Authentication and authorization strategies</li>
                                    <li>Database relationships and Eloquent ORM</li>
                                    <li>API development with Laravel Sanctum</li>
                                </ul>

                                <h2 class="text-2xl font-semibold text-gray-900 mt-8 mb-4">Getting Started</h2>
                                <p>
                                    This tutorial assumes you have a basic understanding of Laravel fundamentals. 
                                    We'll build upon those concepts to create more sophisticated applications.
                                </p>

                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 my-6">
                                    <h3 class="font-semibold text-blue-900 mb-2">💡 Pro Tip</h3>
                                    <p class="text-blue-800">
                                        Make sure to follow along with the code examples in your own Laravel project 
                                        to get the most out of this tutorial.
                                    </p>
                                </div>

                                <p>
                                    Continue reading to dive deep into these advanced Laravel concepts and 
                                    enhance your development skills!
                                </p>
                            </div>
                        </article>
                    @endif

                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <a href="{{ route('user.show', $userId) }}" 
                               class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 transition-colors">
                                ← Back to Profile
                            </a>
                            
                            @if($slug !== 'all-posts')
                                <a href="{{ route('user.posts', ['id' => $userId, 'slug' => 'all-posts']) }}" 
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 transition-colors">
                                    View All Posts
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
