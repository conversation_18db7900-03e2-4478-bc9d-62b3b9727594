<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    // Display a listing of products
    public function productsIndex()
    {
        $products = Product::latest()->get();
        return view('products.index', compact('products'));
    }

    // Show the form to create a new product
    public function create()
    {
        return view('products.create');
    }

    // Store a newly created product in the database
    public function store(Request $request)
    {
        // Validate the input
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        // Store the image
        $imagePath = $request->file('image')->store('products', 'public');

        // Create the product
        Product::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'image' => $imagePath,
        ]);

        // Redirect to the products index page or show a success message
        return redirect()->route('products.index')->with('success', 'Product created successfully!');
    }

    // Display a list of products (index)
    public function index()
    {
        $products = Product::all(); // Or use pagination: Product::paginate(10)
        return view('products.index', compact('products'));
    }
}
