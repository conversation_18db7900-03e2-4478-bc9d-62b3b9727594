<div class="w-64 bg-white shadow-lg hidden lg:block">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Navigation</h3>
        
        <div class="space-y-1">
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Main Pages</h4>
                <a href="{{ route('dashboard') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('dashboard') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    🏠 Dashboard
                </a>
                <a href="/" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->is('/') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    🏡 Home
                </a>
                <a href="/about" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->is('about') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    ℹ️ About
                </a>
                <a href="/public" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->is('public') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    🌐 Public Page
                </a>
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">User Pages</h4>
                <a href="{{ route('user.show', 1) }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    👤 User Profile (ID: 1)
                </a>
                <a href="{{ route('user.show', auth()->id()) }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('user.show') && request()->route('id') == auth()->id() ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    👤 My Profile
                </a>
                <a href="{{ route('user.posts', ['id' => auth()->id(), 'slug' => 'all-posts']) }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('user.posts') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    📝 My Posts
                </a>
                <a href="{{ route('profile.edit') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('profile.edit') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    ⚙️ Edit Profile
                </a>
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Content</h4>
                <a href="{{ route('blogs.index') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('blogs.index') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    📚 Blogs
                </a>
                <a href="{{ route('blogs.create') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('blogs.create') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    ➕ Create Blog
                </a>
                <a href="{{ route('posts.index') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('posts.index') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    📄 Posts
                </a>
                <a href="{{ route('posts.category', 'laravel') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🏷️ Laravel Posts
                </a>
                <a href="{{ route('search.results', 'laravel') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🔍 Search Results
                </a>
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Protected Areas</h4>
                <a href="{{ route('members.only') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('members.only') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    👥 Members Only
                </a>
                <a href="{{ route('verified.only') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('verified.only') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    ✅ Verified Users
                </a>
                <a href="{{ route('premium.content') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('premium.content') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    💎 Premium Content
                </a>
                <a href="{{ route('admin.only') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('admin.only') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    🔐 Admin Only
                </a>
            </div>

            @if(str_contains(strtolower(auth()->user()->name), 'admin'))
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Admin Panel</h4>
                <a href="{{ route('admin.dashboard') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('admin.dashboard') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    🎛️ Admin Dashboard
                </a>
                <a href="{{ route('admin.users') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('admin.users') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    👥 Admin Users
                </a>
                <a href="{{ route('admin.settings') }}" class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('admin.settings') ? 'text-gray-900 bg-gray-100' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }} rounded-md">
                    ⚙️ Admin Settings
                </a>
            </div>
            @endif

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">API Routes</h4>
                <a href="{{ route('api.users') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🔗 API Users
                </a>
                <a href="{{ route('api.posts') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🔗 API Posts
                </a>
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Testing</h4>
                <a href="{{ route('redirect.test') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🔄 Redirect Test
                </a>
                <a href="{{ route('login.redirect') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🔄 Login Redirect
                </a>
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Sample Routes</h4>
                <a href="{{ route('products.show', 123) }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    🛍️ Product (ID: 123)
                </a>
                <a href="/user/456/posts/sample-post" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                    📝 User Post Sample
                </a>
            </div>
        </div>
    </div>
</div>

<div class="lg:hidden bg-white shadow-lg mb-4">
    <div class="p-4">
        <button id="mobile-menu-button" class="flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            Menu
        </button>
        
        <div id="mobile-menu" class="hidden mt-4 space-y-2">
            <a href="{{ route('dashboard') }}" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">🏠 Dashboard</a>
            <a href="/" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">🏡 Home</a>
            <a href="/about" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">ℹ️ About</a>
            <a href="{{ route('user.show', auth()->id()) }}" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">👤 My Profile</a>
            <a href="{{ route('blogs.index') }}" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">📚 Blogs</a>
            <a href="{{ route('profile.edit') }}" class="block px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">⚙️ Edit Profile</a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('mobile-menu-button');
    const menu = document.getElementById('mobile-menu');
    
    if (button && menu) {
        button.addEventListener('click', function() {
            menu.classList.toggle('hidden');
        });
    }
});
</script>
