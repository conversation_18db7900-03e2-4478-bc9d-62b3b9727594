# Session 3: Advanced Routing, Middleware & Authentication

## Hour 1: Theory and Training

### Laravel Routing - 6 Different Ways

#### 1. Basic Closure Routes
Simple routes with anonymous functions for quick prototyping:
```php
Route::get('/about', function () {
    return view('about', ['title' => 'About Us']);
});
```

#### 2. Controller-Based Routes
Routes pointing to controller methods for organized code:
```php
Route::get('/dashboard', [DashboardController::class, 'index']);
```

#### 3. Route Parameters and Constraints
Dynamic routes with validation:
```php
// Required parameters with constraints
Route::get('/user/{id}', function ($id) {
    return "User ID: " . $id;
})->where('id', '[0-9]+');

// Multiple parameters
Route::get('/user/{id}/posts/{slug}', function ($id, $slug) {
    return "User {$id}, Post: {$slug}";
})->where(['id' => '[0-9]+', 'slug' => '[a-z-]+']);

// Optional parameters
Route::get('/posts/{category?}', function ($category = 'general') {
    return "Posts in category: " . $category;
});
```

#### 4. Named Routes
Routes with names for easy URL generation:
```php
Route::get('/products/{id}', function ($id) {
    return "Product ID: " . $id;
})->name('products.show');

// Usage in views: route('products.show', ['id' => 1])
```

#### 5. Route Groups
Grouping routes with common attributes:
```php
// Prefix and middleware groups
Route::prefix('admin')->middleware(['auth', 'verified'])->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return "Admin Dashboard";
    })->name('dashboard');
    
    Route::get('/users', function () {
        return "Admin Users List";
    })->name('users');
});

// API groups
Route::prefix('api/v1')->middleware('auth:sanctum')->name('api.')->group(function () {
    Route::get('/users', function () {
        return response()->json(['users' => 'API Users List']);
    })->name('users');
});
```

#### 6. Resource Routes
RESTful routes for CRUD operations:
```php
// Full resource (7 routes)
Route::resource('blogs', BlogController::class);

// Partial resources
Route::resource('posts', BlogController::class)->only(['index', 'show']);
Route::resource('comments', BlogController::class)->except(['create', 'edit']);
```

### Middleware Implementation

#### Custom Middleware Creation
```bash
php artisan make:middleware CheckPremium
php artisan make:middleware CheckAdmin
```

#### Middleware Logic Examples:
```php
// CheckPremium middleware
public function handle(Request $request, Closure $next): Response
{
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();
    if (!str_contains(strtolower($user->name), 'premium')) {
        abort(403, 'Access denied. Premium subscription required.');
    }

    return $next($request);
}
```

#### Middleware Registration (bootstrap/app.php):
```php
->withMiddleware(function (Middleware $middleware): void {
    $middleware->alias([
        'premium' => \App\Http\Middleware\CheckPremium::class,
        'admin' => \App\Http\Middleware\CheckAdmin::class,
    ]);
})
```

## Hour 2: Hands-on Practice

### Implementation Results

#### Total Routes Created: 54
- **Authentication routes**: 22 (from Breeze)
- **Basic routes**: 8 (closures, parameters)
- **Admin routes**: 3 (grouped with prefix)
- **API routes**: 2 (grouped with prefix)
- **Resource routes**: 14 (blogs: 7, posts: 2, comments: 5)
- **Protected routes**: 5 (middleware demonstrations)

#### Route Categories Implemented:

**1. Public Routes:**
- `/` - Welcome page
- `/about` - About page
- `/public` - Public content

**2. Authentication Routes:**
- `/login`, `/register` - Authentication forms
- `/logout` - Logout action
- `/forgot-password`, `/reset-password` - Password reset

**3. Protected Routes:**
- `/dashboard` - User dashboard (auth + verified)
- `/profile` - Profile management (auth)
- `/members` - Members only (auth)
- `/verified-users` - Verified users only (auth + verified)

**4. Custom Middleware Routes:**
- `/premium` - Premium users only (auth + premium)
- `/admin-only` - Admin users only (auth + admin)

**5. Grouped Routes:**
- `/admin/*` - Admin section (auth + verified)
- `/api/v1/*` - API endpoints (auth:sanctum)

**6. Resource Routes:**
- `/blogs` - Full CRUD operations
- `/posts` - Limited operations (index, show)
- `/comments` - Partial operations (excluding create, edit)

**7. Parameter Routes:**
- `/user/{id}` - User profile with ID constraint
- `/user/{id}/posts/{slug}` - Nested parameters
- `/posts/{category?}` - Optional category parameter
- `/products/{id}` - Named route with constraint
- `/search/{term}` - Search functionality

### Middleware Protection Levels

#### 1. Guest Middleware:
- Allows only non-authenticated users
- Redirects authenticated users to dashboard

#### 2. Auth Middleware:
- Requires user authentication
- Redirects to login if not authenticated

#### 3. Verified Middleware:
- Requires email verification
- Works with auth middleware

#### 4. Custom Middleware:
- **Premium**: Checks for 'premium' in username
- **Admin**: Checks for 'admin' in username

### Route Optimization Features

#### Route Caching:
```bash
php artisan route:cache    # Cache routes for production
php artisan route:clear    # Clear route cache
php artisan route:list     # List all routes
```

#### Route Model Binding:
- Automatic model injection based on route parameters
- Custom key binding for non-ID fields

### Testing Authentication Flow

#### Login Redirection Logic:
1. Unauthenticated users → Login page
2. Authenticated users → Dashboard
3. Failed authentication → Back to login with errors
4. Successful login → Intended page or dashboard

#### Route Protection Testing:
- Public routes accessible to all
- Auth routes require login
- Premium routes require special access
- Admin routes require admin privileges

## Session 3 Completion Status

✅ 6 different routing methods implemented
✅ Route parameters and constraints configured
✅ Named routes for URL generation
✅ Route groups with prefixes and middleware
✅ Resource routes for CRUD operations
✅ Custom middleware created and registered
✅ Authentication flow with multiple protection levels
✅ 54 total routes demonstrating all concepts
✅ Route optimization and caching prepared

## Complete Laravel Curriculum Summary

### Session 1 ✅ Completed:
- Laravel framework introduction
- Installation and environment setup
- Project structure exploration
- Basic Artisan commands

### Session 2 ✅ Completed:
- Laravel Breeze installation
- Authentication scaffolding
- Frontend dependencies setup
- Authentication views and flow

### Session 3 ✅ Completed:
- Advanced routing (6 methods)
- Custom middleware implementation
- Route protection and authentication
- Complete routing system demonstration

## Next Steps for Further Learning:
1. Database relationships and Eloquent ORM
2. Form validation and requests
3. API development with Laravel Sanctum
4. Testing with PHPUnit/Pest
5. Deployment and production optimization

The complete Laravel curriculum has been successfully implemented with hands-on examples and practical demonstrations!
