<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPremium
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // For demonstration, we'll check if user has 'premium' in their name
        // In a real app, you'd check a database field or subscription status
        $user = Auth::user();

        if (!str_contains(strtolower($user->name), 'premium')) {
            abort(403, 'Access denied. Premium subscription required.');
        }

        return $next($request);
    }
}
